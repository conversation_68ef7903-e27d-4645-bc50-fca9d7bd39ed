<template>
  <div class="fom-setting">
    <a-breadcrumb>
      <a-breadcrumb-item><a href="/#/setting/evaluation-form">评价表设置</a></a-breadcrumb-item>
      <a-breadcrumb-item>评价表单{{ route.query.name }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="content">
      <div class="tree-box">
        <p class="total-score">总分：{{ totalScoreOfTree }}</p>
        <div
          class="title-one">
          <div class="add-one" @click="handleAddNew">
            <ys-icon class="icon" type="iconxinzeng"></ys-icon>
            添加打分指标
          </div>
        </div>
        <div class="b-box">
          <a-tree
            v-if="treeData.length > 0"
            show-line
            :fieldNames="{ children: 'children', label: 'name', value: 'id' }"
            :tree-data="treeData"
            @select="onSelect"
            draggable
            @drop="onDrop"
          >
            <template #title="{ dataRef }">
              <div class="title-container">
                <div style="display: flex">
                  <div class="ellipsis" style="max-width: 420px;">{{ dataRef.name }}</div>
                  <span style="color: #8c8c8c;" v-if="dataRef.type === 1 || (dataRef.children && dataRef.children.length > 0)">(合计：{{ dataRef.type === 1 ? dataRef.score : calculateTotalScore(dataRef) }})</span>
                </div>
                <div class="icon-list">
                  <a-tooltip v-if="dataRef.level !== 3 && dataRef.type !== 1">
                    <template #title>添加下级</template>
                    <ys-icon class="icon" type="iconxinzeng" @click.stop="handleAddJunior"></ys-icon>
                  </a-tooltip>
                  <a-tooltip>
                    <template #title>删除</template>
                    <a-popconfirm v-if="!dataRef.children || dataRef.children.length === 0" title="你确定要删除吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete('')">
                      <ys-icon class="icon" type="iconshanchudefuben" @click.stop></ys-icon>
                    </a-popconfirm>
                    <ys-icon v-else class="icon" type="iconshanchudefuben" @click.stop="openDeleteModal(dataRef.id)"></ys-icon>
                  </a-tooltip>
                  <a-tooltip>
                    <template #title>移动</template>
                    <ys-icon class="icon" type="icontuozhuai"></ys-icon>
                  </a-tooltip>
                </div>
              </div>
            </template>
          </a-tree>
          <div class="no-data" v-else>
            <img src="@/assets/images/no_data_1.png" alt="" />
            <div class="no-tips">暂无内容</div>
          </div>
        </div>
      </div>

      <div class="right-end">
        <div class="r-content" v-if="editType !== 0">
          <div class="title">{{ editTitle }}</div>
          <a-form :model="formValue" ref="formRef">
            <a-form-item
              name="name"
              label="指标名称"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 16 }"
              :rules="[
                {
                  required: true,
                  message: '请输入',
                  trigger: 'blur',
                },
              ]"
            >
              <a-input v-model:value="formValue.name" placeholder="请输入" show-count style="width: 328px" :maxlength="100" />
            </a-form-item>
            <a-form-item
              name="type"
              label="类型"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 16 }"
              :rules="[
                {
                  required: true,
                  message: '请选择',
                  trigger: 'blur',
                },
              ]"
            >
              <a-radio-group v-model:value="formValue.type" :options="typeOptions" />
            </a-form-item>
            <a-form-item
              v-if="formValue.type === 1"
              name="score"
              label="分值"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 16 }"
              :rules="[
                {
                  required: true,
                  message: '请输入',
                  trigger: 'blur',
                },
              ]"
            >
              <a-input-number v-model:value="formValue.score" :min="1" :max="9999" placeholder="请输入" />
            </a-form-item>
            <a-form-item
              name="itemDescribe"
              label="描述"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-textarea v-model:value="formValue.itemDescribe" placeholder="请输入" show-count :maxlength="1000" :rows="4" style="width: 328px" />
            </a-form-item>

            <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
              <a-button type="primary" style="margin-right: 16px" @click="handleConfirm">确定</a-button>
              <a-button @click="editType = 0">取消</a-button>
            </a-form-item>
          </a-form>
        </div>
        <div class="no-data" style="margin-top: 200px;" v-else>
          <img src="@/assets/images/no_data_2.png" alt="" />
          <div class="no-tips">请选择左侧目录</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除弹窗 -->
  <a-modal
    v-model:visible="deleteModal"
    :closable="false"
    :footer="null"
    style="width: 416px;"
    dialogClass="delete-modal"
  >
    <h3 class="delete-title">
      <ys-icon class="icon" type="iconyuanxingjinggao" />
      <span>确认删除该指标吗？</span>
    </h3>
    <p style="margin-left: 30px;">将删除该子标所有子节点</p>
    <div class="delete-btn">
      <a-button @click="deleteModal = false" style="margin-right: 8px;">取消</a-button>
      <a-button type="primary" danger @click="handleDelete(deleteId)">删除</a-button>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from "vue";
import { ysIcon } from "@ys/ui";
import { RadioGroupProps } from 'ant-design-vue';
import { useRoute } from "vue-router";
import {
  listFormItem,
  editFormItem,
  deleteFormItem,
  updateFormItemOrder
} from "@/api/aiSettingController";
import { message, type FormInstance } from "ant-design-vue";

const route = useRoute();

const calculateTotalScore = (node: any): number => {
  if (!node || !node.children || node.children.length === 0) {
    return 0;
  }

  return node.children.reduce((sum: number, child: any) => {
    let currentScore = 0;
    if (child.type === 1 && typeof child.score === 'number') {
      currentScore = child.score;
    }

    return sum + currentScore + calculateTotalScore(child);
  }, 0);
};

const totalScoreOfTree = computed(() => {
  let total = 0;
  treeData.value.forEach(rootNode => {
    if (rootNode.type === 1 && typeof rootNode.score === 'number') {
      total += rootNode.score;
    }
    total += calculateTotalScore(rootNode);
  });
  return total;
});

// 获取评分表单
const treeData = ref<any[]>([]);
const editType = ref<number>(0); // 0: 初始状态 1: 添加打分指标 2: 添加下级指标 3: 编辑打分指标
const formValue = ref<FormValue>({
  name: "",
  type: 1,
  score: null,
  itemDescribe: "",
})
interface FormValue {
  name: string;
  type: number;
  score: number | null;
  itemDescribe: string;
}

const getTreeData = async () => {
  const response = await listFormItem({
    id: route.params.tableId as string,
  });
  const res = response.data as any;
  if (res.code === 0) {
    treeData.value = res.data;
  }
}
const onSelect = (selectedKeys: string[], info: any) => {
  selectedNode.value = info.node;
  formValue.value = {
    name: info.node.name,
    type: info.node.type,
    score: info.node.score,
    itemDescribe: info.node.itemDescribe,
  }
  editType.value = 3;
}


// 操作
const selectedNode = ref<any>(null);
const deleteModal = ref<boolean>(false);
const deleteId = ref<string>("");
const handleAddJunior = () => {
  setTimeout(() => {
    editType.value = 2;
    resetForm();
  }, 0);
}
const openDeleteModal = (id: any) => {
  deleteId.value = id;
  deleteModal.value = true;
}
const handleDelete = async (id?: string) => {
  const response = await deleteFormItem({
    formId: route.params.tableId,
    id: id || selectedNode.value.id,
  });
  const res = response.data as any;
  if (res.code === 0) {
    message.success('删除成功');
    getTreeData();
    if (id) {
      deleteModal.value = false;
    }
  }
}
const resetForm = () => {
  formValue.value = {
    name: "",
    type: 1,
    score: null,
    itemDescribe: "",
  }
}
const handleAddNew = () => {
  editType.value = 1;
  resetForm();
}
const onDrop = async ({ node, dragNode, dropPosition }: any) => {
  if (dragNode.level !== node.level) {
    message.warning('只允许同级拖动排序');
    return;
  }

  const params = {
    itemId: dragNode.id,
    newNum: dropPosition,
  }
  const response = await updateFormItemOrder(params);

  const res = response.data as any;
  if (res.code === 0) {
    message.success('移动成功');
    getTreeData();
  } else {
    getTreeData();
  }
}

// 右侧编辑
const editTitle = computed(() => {
  if (editType.value === 1) {
    return "添加打分指标";
  } else if (editType.value === 2) {
    return `${selectedNode.value.name} / 添加下级`;
  } else if (editType.value === 3) {
    return `${selectedNode.value.name} / 编辑`;
  }
})
const typeOptions: RadioGroupProps['options'] = [
  { label: '打分', value: 1 },
  { label: '计算', value: 2 },
]

const formRef = ref<FormInstance>();

const handleConfirm = async () => {
  formRef.value?.validate().then(async () => {
    const { name, type, score, itemDescribe } = formValue.value;
    const params: any = {
      name,
      type,
      score,
      itemDescribe,
      formId: route.params.tableId,
    }

    if (editType.value === 1) {
      params.id = -1;
      params.level = 1;
      params.orderNum = treeData.value.length + 1;
    } else if (editType.value === 2) {
      params.id = -1;
      params.level = selectedNode.value.level + 1;
      params.orderNum = selectedNode.value.children?.length || 0 + 1;
      params.parentId = selectedNode.value.id;
    } else if (editType.value === 3) {
      params.id = selectedNode.value.id;
      params.level = selectedNode.value.level;
      params.orderNum = selectedNode.value.orderNum;
    }

    const response = await editFormItem(params);
    const res = response.data as any;
    if (res.code === 0) {
      message.success('保存成功');
      if (editType.value !== 3) {
        resetForm();
      }
      getTreeData();
    }
  }).catch((err) => {
    console.log('err', err);
  })
}

onMounted(() => {
  getTreeData();
});

watch(treeData, (newVal) => {
  if (newVal.length === 0) {
    editType.value = 0;
  }
}, {
  deep: true,
})
</script>

<style lang="scss" scoped>
$border: #e7e7e7;
$primary: #2469f1;
$txt3: #aab3c9;
$bg: #F7F7F7;
$txt1: #161e5d;

.fom-setting {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  .content {
    display: flex;
    gap: 24px;
    border-radius: 6px;
    height: calc(100vh - 160px);
    margin-top: 20px;
    background-color: #fff;
    .total-score {
      margin-bottom: 20px;
    }
  }
}

.tree-box {
  flex: 1;
  height: 100%;
  padding: 24px;
  padding-right: 48px;
  border-right: 1px solid $border;

  .b-box {
    width: 100%;
    // padding-right: 48px;
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    :deep(.ant-tree-treenode-draggable) {
      width: 100%;
      padding: 0 0 20px 0;
      height: 60px;

      .ant-tree-indent-unit {
        height: 140%;
      }

      .title-container {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        padding-right: 16px;
        .icon-list {
          // display: flex;
          // align-content: center;
          // gap: 10px;
          display: none;
        }
      }

      .ant-tree-treenode-motion {
        width: 100%;
      }
      .ant-tree-indent-unit:before {
        border: 1px dashed $border;
      }
      .ant-tree-switcher {
        display: flex;
        align-items: center;
        padding-left: 12px;
        background: $bg;
        width: 40px;
        border-radius: 5px 0px 0px 5px;
        color: $txt3;
      }
      // .ant-tree-switcher-noop {
      //   display: none;
      // }
      .ant-tree-node-content-wrapper {
        // flex: 1;
        width: 100%;
        background: $bg;

        padding-left: 0;
        border-radius: 0px 5px 5px 0px;
        height: 100%;
      }

      .ant-tree-node-selected .ant-tree-switcher {
        border: 1px solid $primary;
      }

      .text {
        height: 100%;
      }
      &:hover {
        .title-container {
          visibility: visible;
          color: $primary;
          .icon-list {
            display: flex;
            align-content: center;
            gap: 10px;
          }
        }
        .ant-tree-switcher {
          background: #e8f3ff;
          border-left: 1px solid $primary;
          border-top: 1px solid $primary;
          border-bottom: 1px solid $primary;
        }
        .ant-tree-node-content-wrapper {
          background: #e8f3ff;
          border-right: 1px solid $primary;
          border-top: 1px solid $primary;
          border-bottom: 1px solid $primary;
        }
      }
    }

    :deep(.ant-tree-treenode-motion) {
      width: 100%;
    }

    :deep(.ant-tree-treenode-selected) {
      .ant-tree-switcher {
        border-radius: 4px 0 0 4px;
        border: 1px solid $primary;
        border-right: none;
        background: #e8f3ff;

        svg {
          fill: $primary;
        }
      }

      .ant-tree-node-content-wrapper {
        background: #e8f3ff;
        border-radius: 0 4px 4px 0;
        border: 1px solid $primary;
        border-left: none;
        color: $primary;

        .title {
          color: $primary !important;
        }
      }
    }
  }

  .title-one {
    cursor: pointer;
    .add-one {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      width: 100%;
      height: 40px;
      border: 1px dashed #D9D9D9;
      border-radius: 4px;
      margin-bottom: 16px;
    }
  }
}

.right-end {
  width: 40%;
  height: 100%;
  .r-content {
    .title {
      margin: 24px 0;
    }
    div {
      color: $txt1;
    }
  }
}

.no-data {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 118px;
  img {
    width: 200px;
    height: 124px;
  }
  .no-tips {
    color: #8c8c8c;
  }
}

.delete-title {
  display: flex;
  align-items: center;
  .icon {
    font-size: 21px;
    color: #FFAA00;
    margin-right: 10px;
  }
}

.delete-btn {
  text-align: right;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
